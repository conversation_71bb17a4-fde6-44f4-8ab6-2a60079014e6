# 檢查 nvm 是否安裝 ===============================
if (-not (Get-Command nvm -ErrorAction SilentlyContinue)) {
    Write-Error "❌ nvm 未安裝，請先安裝 nvm。"
    exit 1
}

# 取得目前 nvm 版本 ===============================
Write-Host "🔍 Checking current nvm version..."
$nvmVersion = nvm --version
Write-Host "📦 Current nvm version: $nvmVersion"

# 檢查當前使用的 Node.js 版本 ===============================
Write-Host "🔍 Checking current Node.js version..."
$currentNodeVersion = node -v
if ($currentNodeVersion) {
    $currentNodeVersion = $currentNodeVersion.TrimStart('v')
    Write-Host "📦 Current Node.js version: $currentNodeVersion"
} else {
    Write-Host "⚠️ No active Node.js version detected."
    $currentNodeVersion = "0.0.0"  # 設定一個無效版本以確保後面的比較邏輯正常工作
}

# 取得最新的 Node.js LTS 版本 ===============================
Write-Host "🔍 Fetching latest available LTS versions..."
$output = nvm list available
$lines = $output -split "`n"

# 尋找包含 "LTS" 標題的行
$headerLine = $lines | Where-Object { $_ -match "CURRENT.*LTS" }
$headerParts = $headerLine -split '\|'

# 找出 LTS 欄位的索引
$ltsIndex = 0
for ($i = 0; $i -lt $headerParts.Count; $i++) {
    if ($headerParts[$i].Trim() -eq "LTS") {
        $ltsIndex = $i
        break
    }
}

# 跳過標題行，獲取資料行的第一行
$dataLine = $lines | Where-Object { $_ -match "\d+\.\d+\.\d+" } | Select-Object -First 1
$dataParts = $dataLine -split '\|'

# 獲取 LTS 欄位中的版本
$latestLTS = $dataParts[$ltsIndex].Trim()
Write-Host "📦 Latest LTS version: $latestLTS"

# 比較當前版本與最新 LTS 版本是否相同
if ($currentNodeVersion -ne $latestLTS) {
    Write-Host "🔄 Current Node.js version ($currentNodeVersion) differs from latest LTS ($latestLTS)."
    
    # 如果已有 Node.js 版本，先卸載它
    if ($currentNodeVersion -ne "0.0.0") {
        Write-Host "🗑️ Uninstalling current Node.js version..."
        nvm uninstall $currentNodeVersion
    }
    
    # 安裝最新的 LTS 版本
    Write-Host "⬇ Installing Node.js $latestLTS..."
    nvm install $latestLTS
    
    # 切換至該版本
    Write-Host "🔀 Switching to Node.js $latestLTS..."
    nvm use $latestLTS
} else {
    Write-Host "✅ Current Node.js version is already the latest LTS version."
}

# 確保 corepack 有啟用
Write-Host "🛠 Enabling Corepack..."
corepack enable

# 更新 pnpm 為最新版
Write-Host "⚡ Installing/Updating pnpm via Corepack..."
corepack prepare pnpm@latest --activate

# 顯示目前版本資訊
Write-Host "`n✅ All done!"
Write-Host "Node.js version: $(node -v)"
Write-Host "npm version: $(npm -v)"
Write-Host "pnpm version: $(pnpm -v)"