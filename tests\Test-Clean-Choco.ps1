<#
.SYNOPSIS
    Clean-Choco.ps1 腳本的基本測試

.DESCRIPTION
    測試 Clean-Choco.ps1 腳本的各種功能和參數組合

.NOTES
    這是一個基本的測試腳本範例
    實際測試時請小心，避免影響真實的套件安裝
#>

# 測試設定
$ScriptPath = "..\scripts\package-management\Clean-Choco.ps1"
$TestPackageName = "test-package-that-does-not-exist"

Write-Host "開始測試 Clean-Choco.ps1 腳本" -ForegroundColor Cyan
Write-Host "=" * 50

# 測試 1: 檢查腳本是否存在
Write-Host "`n測試 1: 檢查腳本檔案是否存在" -ForegroundColor Yellow
if (Test-Path $ScriptPath) {
    Write-Host "✅ 腳本檔案存在: $ScriptPath" -ForegroundColor Green
} else {
    Write-Host "❌ 腳本檔案不存在: $ScriptPath" -ForegroundColor Red
    exit 1
}

# 測試 2: 檢查腳本語法
Write-Host "`n測試 2: 檢查腳本語法" -ForegroundColor Yellow
try {
    $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $ScriptPath -Raw), [ref]$null)
    Write-Host "✅ 腳本語法正確" -ForegroundColor Green
} catch {
    Write-Host "❌ 腳本語法錯誤: $($_.Exception.Message)" -ForegroundColor Red
}

# 測試 3: 測試 CheckOnly 參數
Write-Host "`n測試 3: 測試 CheckOnly 參數" -ForegroundColor Yellow
try {
    $result = & $ScriptPath -PackageName $TestPackageName -CheckOnly 2>&1
    Write-Host "✅ CheckOnly 參數測試通過" -ForegroundColor Green
} catch {
    Write-Host "❌ CheckOnly 參數測試失敗: $($_.Exception.Message)" -ForegroundColor Red
}

# 測試 4: 測試必要參數驗證
Write-Host "`n測試 4: 測試必要參數驗證" -ForegroundColor Yellow
try {
    $result = & $ScriptPath 2>&1
    if ($result -match "PackageName") {
        Write-Host "✅ 必要參數驗證正常" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 必要參數驗證可能有問題" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✅ 必要參數驗證正常（預期的錯誤）" -ForegroundColor Green
}

# 測試 5: 檢查 Help 資訊
Write-Host "`n測試 5: 檢查 Help 資訊" -ForegroundColor Yellow
try {
    $help = Get-Help $ScriptPath -ErrorAction SilentlyContinue
    if ($help.Synopsis) {
        Write-Host "✅ Help 資訊可用" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Help 資訊可能不完整" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 無法取得 Help 資訊" -ForegroundColor Red
}

# 測試摘要
Write-Host "`n" + "=" * 50
Write-Host "測試完成" -ForegroundColor Cyan

# 注意事項
Write-Host "`n注意事項:" -ForegroundColor Yellow
Write-Host "- 這些是基本的語法和參數測試" -ForegroundColor White
Write-Host "- 實際功能測試需要真實的 Chocolatey 環境" -ForegroundColor White
Write-Host "- 建議在測試環境中進行完整的功能測試" -ForegroundColor White
