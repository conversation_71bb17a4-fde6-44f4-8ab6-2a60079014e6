param(
    [Parameter(Mandatory = $true)]
    [string]$PackageName,          # 套件名稱，例如 "bruno"

    [switch]$CheckOnly,            # 只檢查安裝狀態
    [switch]$CleanOnly,            # 只清理
    [switch]$Reinstall             # 清理後重新安裝
)

function Check-Package($name) {
    $pkg = choco list --local-only-only | Select-String $name
    if ($pkg) {
        Write-Host "📦 $name 目前在 Chocolatey 紀錄中"
    } else {
        Write-Host "❌ $name 未在 Chocolatey 紀錄中"
    }
}

if ($CheckOnly) {
    Write-Host "🔍 檢查模式：只檢查 $PackageName 的安裝狀態"
    Check-Package $PackageName
    exit 0
}

Write-Host "🔍 開始清理 $PackageName ..."

# 1. 嘗試刪除安裝資料夾 (常見安裝路徑)
$installPaths = @(
    "$env:USERPROFILE\AppData\Local\Programs\$PackageName",
    "$env:ProgramFiles\$PackageName",
    "$env:ProgramFiles(x86)\$PackageName"
)

foreach ($path in $installPaths) {
    if (Test-Path $path) {
        Write-Host "📂 刪除安裝目錄: $path"
        Remove-Item -Recurse -Force $path
    }
}

# 2. 刪除 Chocolatey 套件資料夾
$chocoLib = "C:\ProgramData\chocolatey\lib\$PackageName"
if (Test-Path $chocoLib) {
    Write-Host "📂 刪除 Chocolatey lib: $chocoLib"
    Remove-Item -Recurse -Force $chocoLib
}

$chocoLibBad = "C:\ProgramData\chocolatey\lib-bad\$PackageName"
if (Test-Path $chocoLibBad) {
    Write-Host "📂 刪除 Chocolatey lib-bad: $chocoLibBad"
    Remove-Item -Recurse -Force $chocoLibBad
}

# 3. 檢查 Choco 是否還認得該套件
Check-Package $PackageName

# 4. 視參數決定是否重裝
if ($Reinstall) {
    Write-Host "⬇️ 重新安裝 $PackageName ..."
    choco install $PackageName -y
    Write-Host "🎉 清理完成，$PackageName 已重新安裝！"
} elseif ($CleanOnly) {
    Write-Host "🧹 清理完成，未重新安裝 $PackageName"
} else {
    Write-Host "⚠️ 沒有指定 -CleanOnly 或 -Reinstall，僅執行清理"
}
