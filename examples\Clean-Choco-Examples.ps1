# Clean-Choco.ps1 使用範例

# 範例 1: 檢查套件是否已安裝
Write-Host "範例 1: 檢查 bruno 套件狀態" -ForegroundColor Green
# .\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -CheckOnly

# 範例 2: 清理有問題的套件但不重新安裝
Write-Host "`n範例 2: 清理 bruno 套件但不重新安裝" -ForegroundColor Green
# .\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -CleanOnly

# 範例 3: 清理並重新安裝套件
Write-Host "`n範例 3: 清理並重新安裝 bruno 套件" -ForegroundColor Green
# .\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -Reinstall

# 範例 4: 批量處理多個套件
Write-Host "`n範例 4: 批量處理多個有問題的套件" -ForegroundColor Green
$problematicPackages = @("bruno", "postman", "vscode")

foreach ($package in $problematicPackages) {
    Write-Host "處理套件: $package" -ForegroundColor Yellow
    # 先檢查狀態
    # .\scripts\package-management\Clean-Choco.ps1 -PackageName $package -CheckOnly
    
    # 如果需要，執行清理和重新安裝
    # .\scripts\package-management\Clean-Choco.ps1 -PackageName $package -Reinstall
}

# 範例 5: 結合其他 Chocolatey 指令的工作流程
Write-Host "`n範例 5: 完整的套件維護工作流程" -ForegroundColor Green

function Maintain-ChocoPackage {
    param(
        [string]$PackageName
    )
    
    Write-Host "開始維護套件: $PackageName" -ForegroundColor Cyan
    
    # 1. 檢查目前狀態
    Write-Host "1. 檢查套件狀態..." -ForegroundColor Yellow
    # .\scripts\package-management\Clean-Choco.ps1 -PackageName $PackageName -CheckOnly
    
    # 2. 嘗試正常更新
    Write-Host "2. 嘗試更新套件..." -ForegroundColor Yellow
    # choco upgrade $PackageName -y
    
    # 3. 如果更新失敗，執行清理和重新安裝
    Write-Host "3. 如果需要，執行清理和重新安裝..." -ForegroundColor Yellow
    # .\scripts\package-management\Clean-Choco.ps1 -PackageName $PackageName -Reinstall
    
    Write-Host "套件維護完成: $PackageName" -ForegroundColor Green
}

# 使用範例
# Maintain-ChocoPackage -PackageName "bruno"
