# 套件管理腳本 (Package Management Scripts)

此目錄包含與套件管理相關的 PowerShell 腳本，主要用於管理各種套件管理器的套件。

## 腳本清單

### Clean-Choco.ps1
**功能：** Chocolatey 套件清理工具

**描述：** 
用於徹底清理有問題的 Chocolatey 套件，包括刪除安裝目錄、清理快取，並可選擇重新安裝。

**參數：**
- `PackageName` (必要): 要處理的套件名稱
- `CheckOnly` (開關): 僅檢查套件安裝狀態
- `CleanOnly` (開關): 僅清理，不重新安裝
- `Reinstall` (開關): 清理後重新安裝

**使用範例：**
```powershell
# 檢查套件狀態
.\Clean-Choco.ps1 -PackageName bruno -CheckOnly

# 清理並重新安裝
.\Clean-Choco.ps1 -PackageName bruno -Reinstall
```

## 未來規劃

- npm 套件清理工具
- pip 套件管理工具
- winget 套件管理工具
- 套件版本管理工具
