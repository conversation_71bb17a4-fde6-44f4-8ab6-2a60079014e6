# PowerShell 工具包 (PS-Toolkit)

一個收集日常使用及實用 PowerShell 腳本的工具包，旨在提高 Windows 系統管理和開發效率。

## 📁 專案結構

```
ps-toolkit/
├── scripts/                    # 主要腳本目錄
│   ├── package-management/     # 套件管理相關腳本
│   ├── system-utilities/       # 系統工具腳本
│   ├── development-tools/      # 開發工具腳本
│   └── file-operations/        # 檔案操作腳本
├── docs/                       # 文件目錄
├── examples/                   # 使用範例
├── tests/                      # 測試腳本
├── README.md                   # 專案說明
└── LICENSE                     # 授權條款
```

## 🛠️ 腳本清單

### 套件管理 (Package Management)

#### Clean-Choco.ps1
Chocolatey 套件清理工具，用於徹底清理和重新安裝有問題的 Chocolatey 套件。

**功能特色：**
- 檢查套件安裝狀態
- 清理套件安裝目錄和 Chocolatey 快取
- 支援清理後重新安裝
- 多種操作模式選擇

**使用方式：**

```powershell
# 只檢查套件狀態
.\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -CheckOnly

# 只清理，不重新安裝
.\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -CleanOnly

# 清理後重新安裝
.\scripts\package-management\Clean-Choco.ps1 -PackageName bruno -Reinstall

# 預設模式（僅清理）
.\scripts\package-management\Clean-Choco.ps1 -PackageName bruno
```

## 🚀 快速開始

1. **複製專案**
   ```powershell
   git clone <repository-url>
   cd ps-toolkit
   ```

2. **設定執行原則**（如果需要）
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

3. **執行腳本**
   ```powershell
   # 範例：清理 Chocolatey 套件
   .\scripts\package-management\Clean-Choco.ps1 -PackageName <套件名稱> -CheckOnly
   ```

## 📋 系統需求

- Windows 10/11 或 Windows Server 2016+
- PowerShell 5.1 或更新版本
- 管理員權限（部分腳本需要）

## 🤝 貢獻指南

歡迎提交新的實用腳本！請遵循以下規範：

1. **腳本規範**
   - 使用 UTF-8 編碼
   - 包含詳細的函式級註解
   - 遵循 PowerShell 最佳實踐
   - 包含參數驗證和錯誤處理

2. **目錄分類**
   - `package-management/`: 套件管理相關
   - `system-utilities/`: 系統管理工具
   - `development-tools/`: 開發輔助工具
   - `file-operations/`: 檔案處理工具

3. **文件要求**
   - 在 `docs/` 目錄添加詳細說明
   - 在 `examples/` 目錄提供使用範例
   - 更新此 README.md

## 📄 授權條款

本專案採用 [LICENSE](LICENSE) 中指定的授權條款。

## 📞 聯絡資訊

如有問題或建議，請透過 Issues 或 Pull Requests 與我們聯繫。

---

**注意：** 執行腳本前請先閱讀腳本內容，確保了解其功能和影響。部分腳本可能需要管理員權限。