# 安裝指南

本文件說明如何設定和使用 PS-Toolkit。

## 系統需求

### 最低需求
- Windows 10 (版本 1809) 或更新版本
- PowerShell 5.1 或更新版本
- 至少 100MB 可用磁碟空間

### 建議需求
- Windows 11 或 Windows Server 2022
- PowerShell 7.x (PowerShell Core)
- 管理員權限（部分腳本需要）

## 安裝步驟

### 1. 下載專案

#### 使用 Git
```powershell
git clone https://github.com/your-username/ps-toolkit.git
cd ps-toolkit
```

#### 手動下載
1. 從 GitHub 下載 ZIP 檔案
2. 解壓縮到您選擇的目錄
3. 開啟 PowerShell 並切換到專案目錄

### 2. 設定執行原則

PowerShell 預設可能不允許執行腳本。請根據您的需求設定執行原則：

#### 僅針對目前使用者（建議）
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 針對整個系統（需要管理員權限）
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope LocalMachine
```

#### 檢查目前的執行原則
```powershell
Get-ExecutionPolicy -List
```

### 3. 驗證安裝

執行以下指令來驗證安裝：

```powershell
# 檢查 PowerShell 版本
$PSVersionTable.PSVersion

# 測試腳本執行
.\scripts\package-management\Clean-Choco.ps1 -PackageName test -CheckOnly
```

## 設定環境變數（選用）

為了方便使用，您可以將 PS-Toolkit 目錄加入到 PATH 環境變數中：

### 暫時設定（僅限目前 PowerShell 工作階段）
```powershell
$env:PATH += ";C:\path\to\ps-toolkit\scripts"
```

### 永久設定
```powershell
# 取得目前的 PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# 加入 PS-Toolkit 路徑
$newPath = $currentPath + ";C:\path\to\ps-toolkit\scripts"

# 設定新的 PATH
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
```

## PowerShell 設定檔設定（選用）

您可以在 PowerShell 設定檔中加入常用的別名和函式：

### 1. 找到設定檔位置
```powershell
$PROFILE
```

### 2. 建立或編輯設定檔
```powershell
# 如果設定檔不存在，建立它
if (!(Test-Path -Path $PROFILE)) {
    New-Item -ItemType File -Path $PROFILE -Force
}

# 編輯設定檔
notepad $PROFILE
```

### 3. 加入 PS-Toolkit 相關設定
```powershell
# PS-Toolkit 設定
$PSToolkitPath = "C:\path\to\ps-toolkit"

# 常用別名
Set-Alias -Name clean-choco -Value "$PSToolkitPath\scripts\package-management\Clean-Choco.ps1"

# 常用函式
function Get-PSToolkitHelp {
    Get-Content "$PSToolkitPath\README.md"
}
```

## 疑難排解

### 執行原則問題
如果遇到執行原則錯誤，請參考上述「設定執行原則」章節。

### 權限問題
某些腳本需要管理員權限。請以管理員身分執行 PowerShell：
1. 按 Win + X
2. 選擇「Windows PowerShell (系統管理員)」

### 路徑問題
確保腳本路徑正確，使用絕對路徑或正確的相對路徑。

### 相依性問題
某些腳本可能需要特定的工具或套件（如 Chocolatey）。請確保已安裝相關相依性。

## 更新

### 使用 Git 更新
```powershell
git pull origin main
```

### 手動更新
1. 下載最新版本
2. 備份您的自訂腳本
3. 替換舊檔案
4. 恢復自訂腳本

## 解除安裝

1. 刪除專案目錄
2. 移除環境變數設定（如有設定）
3. 清理 PowerShell 設定檔中的相關設定（如有設定）
