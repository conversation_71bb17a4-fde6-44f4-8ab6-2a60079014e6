param(
    [Parameter(Mandatory=$false)]
    [string]$RootFolder = (Get-Location).Path
)

# 確認根資料夾存在
if (-not (Test-Path -Path $RootFolder -PathType Container)) {
    Write-Error "指定的資料夾 '$RootFolder' 不存在！"
    exit 1
}

# 獲取所有子資料夾
$subFolders = Get-ChildItem -Path $RootFolder -Directory

Write-Host "開始掃描資料夾：$RootFolder"
Write-Host "找到 $($subFolders.Count) 個子資料夾"
Write-Host "----------------------------"

# 記錄成功和失敗的資料夾
$successful = @()
$failed = @()

# 目前位置
$currentLocation = Get-Location

foreach ($folder in $subFolders) {
    Write-Host "處理資料夾: $($folder.FullName)" -ForegroundColor Cyan
    
    try {
        # 切換到子資料夾
        Set-Location -Path $folder.FullName
        
        # 檢查是否為 git 倉庫
        $isGitRepo = Test-Path -Path ".git" -PathType Container
        
        if ($isGitRepo) {
            Write-Host "  執行 git 更新..." -ForegroundColor Yellow
            
            # 儲存原始分支
            $originalBranch = (git rev-parse --abbrev-ref HEAD 2>$null)
            if ($LASTEXITCODE -ne 0) {
                $originalBranch = "unknown"
            }
            Write-Host "  原始分支: $originalBranch" -ForegroundColor Gray
            
            # 獲取遠端分支資訊
            Write-Host "  獲取遠端分支資訊..." -ForegroundColor Yellow
            git fetch --all --prune 2>$null
            if ($LASTEXITCODE -ne 0) {
                Write-Host "  獲取遠端分支資訊失敗" -ForegroundColor Red
                $failed += $folder.Name
                continue
            }
            
            # 獲取所有遠端分支
            $remoteBranches = git branch -r | Where-Object { $_ -match "origin/(.*)" } | ForEach-Object { $matches[1] }
            
            # 移除不需要處理的特殊分支
            $remoteBranches = $remoteBranches | Where-Object { $_ -notmatch "HEAD" -and $_ -notmatch "\->" }
            
            if ($remoteBranches.Count -eq 0) {
                Write-Host "  沒有發現遠端分支，跳過" -ForegroundColor Yellow
                $failed += $folder.Name
                continue
            }
            
            Write-Host "  發現以下遠端分支：" -ForegroundColor Gray
            $remoteBranches | ForEach-Object { Write-Host "    - $_" -ForegroundColor Gray }
            
            $branchUpdated = $false
            
            # 更新每個遠端分支
            foreach ($branch in $remoteBranches) {
                # 檢查本地是否已有此分支
                $localBranch = git branch | Where-Object { $_ -match "\s+$branch$" }
                
                if ($null -eq $localBranch) {
                    # 如果本地沒有此分支，創建並追蹤
                    Write-Host "  創建本地分支: $branch..." -ForegroundColor Yellow
                    git checkout -b $branch --track origin/$branch 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        Write-Host "  無法創建本地分支 $branch" -ForegroundColor Red
                        continue
                    }
                } else {
                    # 如果本地已有此分支，切換到該分支
                    Write-Host "  切換到分支: $branch..." -ForegroundColor Yellow
                    git checkout $branch 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        Write-Host "  無法切換到分支 $branch" -ForegroundColor Red
                        continue
                    }
                }
                
                # 拉取更新
                Write-Host "  拉取分支 $branch 的更新..." -ForegroundColor Yellow
                git pull 2>$null
                if ($LASTEXITCODE -ne 0) {
                    Write-Host "  拉取分支 $branch 失敗" -ForegroundColor Red
                } else {
                    Write-Host "  分支 $branch 更新成功" -ForegroundColor Green
                    $branchUpdated = $true
                }
            }
            
            # 如果至少有一個分支更新成功，則視為成功
            if ($branchUpdated) {
                # 回到原始分支
                if ($originalBranch -ne "unknown") {
                    Write-Host "  切換回原始分支: $originalBranch..." -ForegroundColor Gray
                    git checkout $originalBranch 2>$null
                    if ($LASTEXITCODE -ne 0) {
                        Write-Host "  無法切換回原始分支，請手動切換" -ForegroundColor Yellow
                    }
                }
                
                Write-Host "  成功完成！" -ForegroundColor Green
                $successful += $folder.Name
            } else {
                Write-Host "  所有分支更新失敗" -ForegroundColor Red
                $failed += $folder.Name
            }
        } else {
            Write-Host "  跳過 - 不是 git 倉庫" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "  錯誤: $_" -ForegroundColor Red
        $failed += $folder.Name
    }
    finally {
        # 返回原始位置
        Set-Location -Path $currentLocation
    }
    
    Write-Host "----------------------------"
}

# 顯示總結
Write-Host "執行完成！" -ForegroundColor Green
Write-Host "成功處理的資料夾: $($successful.Count)" -ForegroundColor Green
if ($successful.Count -gt 0) {
    Write-Host ($successful -join ", ") -ForegroundColor Green
}

Write-Host "失敗的資料夾: $($failed.Count)" -ForegroundColor $(if ($failed.Count -gt 0) { "Red" } else { "Green" })
if ($failed.Count -gt 0) {
    Write-Host ($failed -join ", ") -ForegroundColor Red
}