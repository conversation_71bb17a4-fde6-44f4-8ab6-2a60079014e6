# 貢獻指南

感謝您對 PS-Toolkit 專案的興趣！我們歡迎各種形式的貢獻。

## 如何貢獻

### 1. 提交新腳本

#### 腳本規範
- **編碼格式**: 使用 UTF-8 編碼
- **註解要求**: 包含詳細的函式級註解
- **參數驗證**: 使用 PowerShell 參數驗證屬性
- **錯誤處理**: 包含適當的錯誤處理機制
- **輸出格式**: 使用一致的輸出格式和顏色

#### 腳本範本
```powershell
<#
.SYNOPSIS
    腳本的簡短描述

.DESCRIPTION
    腳本的詳細描述，包括功能和用途

.PARAMETER ParameterName
    參數的描述

.EXAMPLE
    使用範例

.NOTES
    作者資訊和版本資訊
#>

param(
    [Parameter(Mandatory = $true, HelpMessage = "參數說明")]
    [ValidateNotNullOrEmpty()]
    [string]$ParameterName
)

# 主要功能實作
try {
    # 腳本邏輯
    Write-Host "執行成功" -ForegroundColor Green
}
catch {
    Write-Error "執行失敗: $($_.Exception.Message)"
    exit 1
}
```

### 2. 目錄分類

請將腳本放置在適當的目錄中：

- **package-management/**: 套件管理相關腳本
- **system-utilities/**: 系統管理和維護工具
- **development-tools/**: 開發輔助工具
- **file-operations/**: 檔案和目錄操作工具

### 3. 文件要求

提交新腳本時，請同時提供：

1. **腳本內註解**: 詳細的 PowerShell 註解區塊
2. **README 更新**: 更新相關目錄的 README.md
3. **使用範例**: 在 `examples/` 目錄提供使用範例
4. **測試腳本**: 如可能，提供基本的測試腳本

### 4. 提交流程

1. Fork 此專案
2. 建立功能分支 (`git checkout -b feature/new-script`)
3. 提交變更 (`git commit -am 'Add: 新增 XXX 腳本'`)
4. 推送到分支 (`git push origin feature/new-script`)
5. 建立 Pull Request

### 5. 程式碼審查

所有提交都會經過程式碼審查，審查重點包括：

- 程式碼品質和可讀性
- 安全性考量
- 效能影響
- 文件完整性
- 測試覆蓋率

## 問題回報

如果發現問題，請透過 GitHub Issues 回報，並包含：

- 問題描述
- 重現步驟
- 預期行為
- 實際行為
- 系統環境資訊

## 功能建議

歡迎提出新功能建議！請在 Issues 中描述：

- 功能需求
- 使用場景
- 預期效益
- 實作建議（如有）

## 行為準則

請遵守以下準則：

- 尊重所有參與者
- 建設性的討論和回饋
- 專注於技術問題
- 保持專業態度

感謝您的貢獻！
